要素：

1. 评测能力：如分子性质预测、逆合成分析、反应产率预测、化学知识问答等
2. 任务定义：具体任务类型和格式
3. 数据集：数据集来源、规模、数据格式（如SMILES, SELFIES）及关键的预处理步骤
4. 评估方法：
   * 评估指标（例如：Accuracy, F1 Score, MAE, R², Molecule Similarity等
   * 评估脚本，并提供其来源或链接
5. 基线模型及SOTA：列出基线模型及其表现
6. 输入输出格式(prompt)
7. 排行榜：说明该基准是否有公开的排行榜

# 1.ScienceQA(NeurIPS 2022)

github链接:https://github.com/lupantech/ScienceQA

项目主页链接：https://scienceqa.github.io/

论文：https://arxiv.org/abs/2209.09513

## 1.1评测任务

类型：多选

格式：问题+上下文+辅助信息

**辅助信息：**

* Lecture：与问题相关的背景知识文本（83.9%）
* Explanation：解题思路（91.3%）

图像和文本上下文的比例如下：

<img src="./可用bench.assets/x4-1757468092559-1.png" alt="Refer to caption" style="zoom:50%;" />

样例：

![image-20250910094919294](./可用bench.assets/image-20250910094919294.png)

**学科**：natural science, language science, and social science

**主题**：如生物学、物理学、化学等

**分类：**如植物、细胞、动物（有26个主题、129个类别和379项技能）

**grade：**k-12年级水平，来源于IXL Learning，难度为中小学

**skill：**对任务的描述

![Refer to caption](./可用bench.assets/x5.png)

可用的是Chemistry下的四个分类，用于学习基础知识

## 1.2构建方法

1. **来源：**IXL Learning。
2. **提取：**启发式规则提取（例如问题、提示、图像、选项、答案、讲座和解决方案）
3. **清洗与校验：**
   * 移除了无效问题（如只有一个选项、数据错误、重复问题）
   * 对于多答案问题，只保留一个正确答案
   * 随机打乱了选项顺序，以避免模型学习到位置偏见
4. **格式化：**使用半自动化的脚本对提取出的讲座和解决方案进行重新格式化，使其结构更清晰（如保留表格、列表等结构）
5. **质量保证：**开发了一个数据探索工具来审查收集数据集中的示例，并且由专家进一步手动修正了错误的标注

**划分**：**training**, **validation**, and **test** splits with a ratio of 60:20:20（随机分）

**长度：**长度和样式设置不同

## 1.3评测方法与指标

**评估指标：**

1. **多选：**Accuracy
2. **解释生成（CoT）：**给出问题、上下文、多选，要求生成待解释的答案（AE）；带讲解和解释的答案
   * **BLEU-1/4**：评估生成文本与参考文本在 n-gram 上的重合度。
   * **ROUGE-L**：基于最长公共子序列，评估文本的流畅度和相似性。
   * **Similarity**：基于 Sentence-BERT 计算生成文本与参考文本之间的语义相似度得分 。
   * **人工评估：**对解释的相关性、正确性和完整性进行评分，都满足定为黄金标准 (Gold)。

## 1.4 基线模型与排行榜

排行榜见https://scienceqa.github.io/leaderboard.html
SOTA模型为：**Mutimodal-T-SciQ_Large**https://arxiv.org/abs/2305.03453（96.18%）

## 1.5 Prompt

对基线模型的标准输入->输出格式为：

​	问题+上下文+选项->答案

同时论文做了消融实验，证明了：

1. 图像/文本上下文重要
2. CoT重要

包含1-shot1示例的训练指令$\{{I_i}\}$和测试指令$I_t$

![image-20250910152330893](./可用bench.assets/image-20250910152330893.png)

使用GPT-3 CoT的示例如下：

![Refer to caption](./可用bench.assets/x6.png)

## 1.6 质量评估

做消融，验证数据集是否存在bias或shortcuts

**盲研究：**遮住部分上下文，模型如果acc下降多，说明数据集稳健可靠。（MLLM无法通过部分信息就猜出结果）

完整的上下文是ALE（answer+Lecture+Explanation）

* 只提供Q或$C_I$结果接近随机概率
* Q+M 、Q+$C_T$+M 、 Q+$C_I$+M 仅的性能下降

![image-20250910150927287](./可用bench.assets/image-20250910150927287.png)

**上下文：**研究示例对ACC的影响，标明提供其他问题的ALE实例更好，同时两个示例较好。

**顺序：**论文做了不同输出格式测试，表明QCM->ALE的结果最好。（与LA、EA、LEA、ELA做对比），推测原因可能是L和E可能会比较长，导致模型token耗尽或中途停下了。

## 1.7 缺点

太过简单

# 2.SceMQA 大学入学水平多模态问答

github链接：https://scemqa.github.io/

## 2.1 构建方法

1. **人工解释：**为问题提供人工验证解释，解释有助于识别模型预测中的错误，也可以进行SFT
2. **知识分类：**问题被分类到特定的知识组件中，从而便于对模型进行详细的知识追踪（？）
3. **问题变化：**包含具有相同上下文但不同问题的题目（基于同一图像或问题），论文指出，解决此类相同情景的细微差别问题，对AI具有挑战性

<img src="./可用bench.assets/x4.png" alt="Refer to caption" style="zoom:80%;" />

## 2.2 测评能力

包含四个科学题目：数学、物理、化学和生物学

两种类型题目：多选和自由回答

自由回答：要求模型输出一个精确，简短的回答

<img src="./可用bench.assets/x2.png" alt="Refer to caption" style="zoom:80%;" />

<img src="./可用bench.assets/image-20250910002525987.png" alt="image-20250910002525987" style="zoom:50%;" />

**难度：**

<img src="./可用bench.assets/image-20250908181741232.png" alt="image-20250908181741232" style="zoom:80%;" />

## 2.3 测评方法与指标

三种设置

1. **零样本：**模型在没有任何先前示例的情况下被提供问题
2. **少样本：**向模型提供少量示例问题和解决方案进行“学习”（由于在一个 API 调用中插入多张图片不够灵活，论文使用手工制作的纯文本问题作为示例）
3. **纯文本：**一种特殊的零样本，仅向模型提供问题的文本内容，不包含任何伴随图片

评估指标：

基于精确匹配的准确率。（因为选择题和开放式问题都有明确的、唯一的正确答案？）

使用 GPT4 作为开放式问题的评估者



## 2.4 基线模型与表现

前三个开源、后三个闭源

1. InstructBLIP
2. MiniGPT4
3. LLaVa
4. Yi-VL（多选SOTA，37.14%）
5. Deepseek-VL-Chat-7B（自由回答SOTA，14%）
6. InternLM-XComposer2
7. Qwen-VL-chat
8. Google Bard
9. Gemini Pro
10. GPT4-V（多选SOTA，60.83%,自由回答SOTA，36%）

排行榜见https://scemqa.github.io/#leaderboard

## 2.5错误原因

文章分了四类：

1. **推理错误：**模型正确处理基于图像的信息，但未能构建准确的推理链以得出正确答案（遗漏必要步骤、进行错误计算）-提示工程或监督微调可能有益
2. **图像感知错误：**模型错误地解释视觉信息（错误地读取数字或坐标，或无法区分几何图中的点）-结合外部工具-OCR
3. **缺乏知识：**模型未能正确识别或应用相关知识概念（误用公式、误解定理）。在生化物领域常见。
4. **拒绝回答或标注错误：**拒绝回答是指模型拒绝提供答案（不确定或无法理解问题），标注错误源于数据集标注中的不准确或不一致，导致模型混淆。-要创建稳健数据集；锻炼模型有效处理模糊或复杂查询。

# 3.MMMU /MMMU-Pro

MMMU-github链接：https://github.com/MMMU-Benchmark/MMMU?tab=readme-ov-file

MMMU-Prohuggingface链接：https://huggingface.co/datasets/MMMU/MMMU_Pro

MMMU评估脚本链接：https://github.com/MMMU-Benchmark/MMMU/tree/main/mmmu1

MMMU-Pro评估脚本链接：https://github.com/MMMU-Benchmark/MMMU/tree/main/mmmu-pro

是通用多模态benchmark

## 3.1 构建方法

1. 过滤掉纯文本可回答的问题：确保问题更侧重于多模态理解而非纯粹的文本理解。

   1. 用四个LLM过滤，要求在没有图像的情况下回答问题，重复十次，对超过五次就视为“可回答”
   2. 排除四个模型在大多数实验中正确回答的问题

2. 增强候选选项：减少基于选项猜测（选项可能有相关，MLLM看选项就理解了问题）

   1. GPT-4o生成选项

   2. claude过滤

   3. 人工审核选项和问题（1. 确保选项是多样的，逻辑上不同，没有歧义 2. 对问题和选项进行交叉验证，消除不一致问题）

3. 纯视觉输入设置：模型只接受一张包含所有信息的图，模拟人类整合视觉和文本信息的核心认知技能。（给一个问题的截图）

![image-20250909100023658](./可用bench.assets/image-20250909100023658.png)

## 3.2 评测能力

MMMU包括从**大学考试、测试和教科书**收集的多模态问题，涵盖六个核心学科：艺术与设计、商业、科学、健康与医学、人文与社会科学以及技术与工程。

![algebraic reasoning](./可用bench.assets/mmlu_example.jpeg)

## 3.3 评估方法

评估脚本见github

## 3.4 基线模型与排行榜

包含了主流开闭源模型与人类专家

部分截图：https://huggingface.co/datasets/MMMU/MMMU
![image-20250909225100338](./可用bench.assets/image-20250909225100338.png)

## 3.5 输出与输出（prompt）

**COT:**

文章指出，使用CoT比标准设置正确率高。左为chatGPT，右llava ov 72B

![image-20250909102902580](./可用bench.assets/image-20250909102902580.png)

**OCR提示：**

文章指出，LLM的OCR能力已经很强，即使不带明确的OCR提示词，准确率也不会下降太多。

文章也指出，OCR能力好的模型不一定在MMMU-Pro上回答就好

![image-20250909103238437](./可用bench.assets/image-20250909103238437.png)



**视觉输入：**

文章指出，将文本和视觉信息整合在一张图片，模型虽然能准确识别图片，但分析能力会变得更浅显，也更容易出错。

所有Prompt如下
![image-20250909103230329](./可用bench.assets/image-20250909103230329.png)





# 4. MMLU-Pro（NeurIPS-24）

huggingface：https://huggingface.co/datasets/TIGER-Lab/MMLU-Pro

MMLU-PRO：https://arxiv.org/abs/2406.01574

## 4.1 评测任务

覆盖：14个领域，12000个问题，其中3.41%是化学

![image-20250911094345181](可用bench.assets/image-20250911094345181.png)

## 6.2 构建方法

**数据来源：**原始MMLU、STEM网站（包含STEM问题）、TheoremQA（人工标注的问题）、SciBench（大学考试的高级科学问题）

**数据格式：**自然语言

![Refer to caption](./可用bench.assets/data_collection_2.png)

1. **初步筛查：**用八个LLM评估问题（Llama-2-7B、Llama-2-7B-Chat、Llama-2-13B、Llama-2-13B-Chat、Mistral-7B、Gemma-7B、Yi-6B 和 Yi-6B-Chat），超过四个模型回答正确就视为过于简单，然后排除
2. **问题收集与整合：**
   * 从STEM、TheoremQA、SciBench扩展问题，（但STEM的问题是带有解决方案的陈述句，TheoremQA 中的问题附带简短答案）
   * 用GPT-4-Turbo提取简短答案，转为标准多选，以匹配现有数据集格式
   * 并增加选项为四个（四个是MMLU的标准）
3. **选项增强：**用GPT-4-Turbo将问题从4->10。
4. **专家评审：**
   * **正确性和适用性验证**:专家移除不适合选择题格式的题目，移除缺乏必要信、或需要非文本元素（如图片或表格）的题目
   * **确保干扰项有效性:**Gemini-1.5-Pro 识别“假负例”（错误标记为干扰项的正确答案）

## 6.2 评测方法与指标

评测指标是ACC

## 6.3 Prompt

全局提示词：

```
The following are multiple-choice questions (with answers) about physics. Think step by step and then finish your answer with "The answer is (X)" where X is the correct letter choice.
```

**5-shot Chain-of-Thought (CoT) prompt：**

​	给出五个示例，然后给出**Question:** <question> 和**Options:** <options> 

```
Question: <示例问题1>
Options: <示例选项1>
Answer: Let's think step by step. <详细的推理和计算过程1>. The answer is (X).

... (重复5次) ...

Question: <需要模型回答的问题>
Options: <需要模型回答的选项>
Answer: Let's think step by step.
```



## 6.3 基线模型与排行榜

部分排行如下：

![image-20250910172703194](./可用bench.assets/image-20250910172703194.png)

## 6.4 缺点

要求的计算-加减乘除和指数运算太简单，不能充分评估LLMs的推理能力深度



# 5. HALLUSIONBENCH



## 5.1评测任务

**语言幻觉：**

1. LLM不依靠视觉输入，而是文本
2. 视觉输入被误解

定义了两种视觉问题：

1. **视觉依赖：**必须要结合视觉上下文才能有肯定答案（评估视觉识别和推理）
2. **视觉补充：**没有视觉上下文也能回答，视觉做补充或更正
   * 评估先验知识缺乏是否会导致幻觉
   * 评估有足够的先验知识，是否还查看视觉上下文来补充和增强相应
   * 评估先验知识和视觉上下问冲突的时候表现
   * 评估解释密集视觉信息的能力

## 5.2 构建过程

1. 收集图：收集多种主题和类型，写问答对，一个图3.26个问题

   ![image-20250911193510858](可用bench.assets/image-20250911193510858.png)

2. 编辑图：图像翻转、顺序反转、遮罩、光学字符编辑、对象编辑和颜色编辑（鲁棒性）

## 5.3 评估指标

做判断

1. **模型回答**：“yes”, “no”, “uncertain”；真实答案：“yes”, “no”

2. GPT4-裁判：评判，输出Incorrect (0), Correct (1), or Uncertain (2) 。裁判三次

如果Uncertain (2)没有给出视觉输入，那么也认为是正确的。

**正确性评估指标 (Correctness Evaluation Metrics)**

这些指标用于衡量模型回答的整体准确性。

hard acc用于评估脆弱性：是否会被图像篡改误导

| 指标名称                          | LaTeX 公式                                                   | 描述                                                         |
| --------------------------------- | ------------------------------------------------------------ | ------------------------------------------------------------ |
| **All Accuracy** (aAcc)           | $$ \text{aAcc} = \frac{1}{|\mathcal{V}|} \sum_{(I,q) \in \mathcal{V}} \mathbf{1}\left( \mathcal{M}(I,q) = y_{(I,q)} \right) $$ | 计算所有问题-图像对的平均准确率 。                           |
| **Figure Accuracy** (fAcc)        | $$ \text{fAcc} = \frac{1}{|\mathcal{I}|} \sum_{I \in \mathcal{I}} \mathbf{1}\left( \bigwedge_{q \in \mathcal{Q}_I} \mathcal{M}(I,q) = y_{(I,q)} \right) $$ | 衡量模型对**同一张图片**的所有相关问题都能正确回答的能力。只有当与该图片相关的所有问题都答对时，该图片才算正确。 |
| **Question Pair Accuracy** (qAcc) | $$ \text{qAcc} = \frac{1}{|\mathcal{Q}|} \sum_{q \in \mathcal{Q}} \mathbf{1}\left( \bigwedge_{I \in \mathcal{I}_q} \mathcal{M}(I,q) = y_{(I,q)} \right) $$ | 衡量对于**同一个问题**，模型能否在所有相关的图片（例如，原图和编辑过的图）上都给出正确答案 。 |

> **符号说明**：
> - $\mathcal{V}$: 所有评估的图像-问题对集合。
> - $\mathcal{I}$: 所有图像集合，$\mathcal{Q}$: 所有问题集合。
> - $\mathcal{Q}_I$: 与图像 $I$ 相关的问题集合。
> - $\mathcal{I}_q$: 与问题 $q$ 相关的图像集合。
> - $\mathbf{1}(\cdot)$: 指示函数，条件为真时取 1，否则取 0。
> - $y_{(I,q)}$: 图像 $I$ 和问题 $q$ 的真实标签。
> - $\mathcal{M}(I,q)$: 模型对 $(I,q)$ 的预测输出。

---

**分析性评估标准 (Analytical Evaluation Criteria)**

这些指标旨在从不同维度诊断模型的特定失败模式，如偏见和逻辑一致性。

| 指标名称                                  | LaTeX 公式                                                   | 描述                                                         |
| ----------------------------------------- | ------------------------------------------------------------ | ------------------------------------------------------------ |
| **Yes Percentage Difference** (Pct. Diff) | $$ d_y = \frac{1}{|\mathcal{V}|} \sum_{(I,q) \in \mathcal{V}} \left[ \mathbf{1}\left( \mathcal{M}(I,q) = \text{"yes"} \right) - \mathbf{1}\left( y_{(I,q)} = \text{"yes"} \right) \right] $$ | 衡量模型回答“Yes”的频率与真实答案为“Yes”的频率之间的差异。$\|d_y\|$ 越接近 1，说明模型偏见越强。 |
| **False Positive Ratio** (FP Ratio)       | $$ r_{fp} = \frac{1}{|\mathcal{W}|} \sum_{(I,q) \in \mathcal{W}} \mathbf{1}\left( \mathcal{M}(I,q) = \text{"yes"} \right) $$ | 在所有回答**错误**的问题中，模型回答“Yes”的比例。$r_{fp}$ 越接近 0.5，说明模型在犯错时没有明显的“Yes”偏好，表现更稳健。其中 $\mathcal{W} = \{(I,q) \in \mathcal{V} \mid \mathcal{M}(I,q) \ne y_{(I,q)}\}$ 是所有预测错误的样本集合。 |
| **Consistency Test**                      | 使用 **fAcc** (Figure Accuracy)                              | 该测试通过 fAcc 指标来衡量模型的逻辑一致性。因为一组问题 $\mathcal{Q}_I$ 在设计上是逻辑相关的，如果模型只答对了其中的一部分，则被认为是不一致的。 |
| **Diagnostic Test**                       | —                                                            | 该测试使用决策树（如图3所示）来将模型的失败案例归类为**语言幻觉 (Language Hallucination)**、**视觉错觉 (Visual Illusion)** 或**混合/不确定 (Mixed/Uncertain)** 。它计算的是这些失败类型在所有失败案例中所占的百分比。 |

**故障类型：**

* 语言幻觉：没有视觉输入乱答
* 视觉错觉：不能正确识别和理解图
* 混合/不确定

![image-20250911195619211](可用bench.assets/image-20250911195619211.png)

## 5.4 prompt

chatgpt4评估的提示词：

```
Imagine you are an intelligent teacher. Thoroughly read
the question, reference answer, and the prediction answer
to ensure a clear understanding of the information provided.
Assess the correctness of the predictions. If the prediction
answer does not conflict with the reference answer, please
generate “correct”. If the prediction answer conflicts with
the reference answer, please generate “incorrect”. If the prediction answer is unclear about the answer, please generate
"unclear".
```

# 6.ChemCoTBench

## 6.1 提出原因

以往的化学基准测试侧重于领域知识的事实回忆（例如，命名化合物或反应），而我们的 ChemCoTBench 则通过定义一组模块化化学操作，专注于评估复杂化学问题的**逐步推理**能力。也就是评估LLMs **如何实现**问题的。

## 6.2 创新点

1. 定义模块化化学操作：认为复杂化学任务可以分解为一系列基础的**模块化学操作**
2. 实现精细化评估：对最终结果评估，还要说明“where、why、how”提高了评估的粒度，便于定位错误原因（知识、推理能力不足）

## 6.2 任务定义

**分子理解：**

1. 官能团与环的计数：评估模型对smiles的理解和对化学拓扑结构的感知
2. 复杂骨架结构识别：Murcko 骨架、环系（稠环和桥环）
3. SMILES等效性判别：通过分子结构的排列和突变，测试模型能否识别出化学上等同但SMILES表示形式不同的分子，考查鲁棒性

**分子编辑：**评估是否能执行基本的分子编辑操作，并抱持化学分子的有效性

1. 添加
2. 删除
3. 取代官能团

**分子优化：**

1. 物理化学级别：提高 LogP、Solubility和 QED 以改善药物相似性
2. **靶点级别**：提高 DRD2、GSK3- β 和 JNK3 靶点的结合亲和力（需要理解药物-靶点相互作用）

![Refer to caption](可用bench.assets/x1.png)

**反应预测：**

1. 正向预测：为了评估推理，要求模型先推断可能的反应类型，再预测主要产物和副产物，
2. 单步逆合成：给定产物和试剂，通过识别关键键断裂和官能团转化（在约束条件下），预测反应物
3. 反应条件预测：预测催化剂、溶剂和试剂
4. 反应机理理解：下一步基元步骤产物预测（逐步预测中间体，测试电子流动建模）和机理路线选择（从替代方案中选择最可能的路径，评估机理推理）

## 6.3 评估指标

| 任务类别 | 子任务               | 评估指标                                                     |
| -------- | -------------------- | ------------------------------------------------------------ |
| 分子理解 | 官能团/环识别        | 平均绝对误差 (Mean Absolute Error, MAE)                      |
|          | Murcko骨架提取       | Tanimoto分子相似度                                           |
|          | SMILES等效性         | 二元决策，准确率 (Accuracy)                                  |
| 分子编辑 | 添加/删除/替换官能团 | Pass@1 (评估编辑后的分子是否满足指令)                        |
| 分子优化 | 各项性质优化         | 成功率 (Success Rate, SR%) 和平均性质改善值 (Δ)              |
| 反应预测 | 机理路径选择         | 构建为多选，用准确率 (Accuracy)                              |
|          | 产物/反应物/条件生成 | 构建为smiles生成，Top-1准确率 和 基于分子指纹的相似度 (FTS)【使用Morgan、MACCS、RDKit】 |















#  问题

1. 如果只用题目+ACC，如何评估模型处理实际下游任务的能力？（参考https://ar5iv.labs.arxiv.org/html/2211.09110？Mol-Instructioins？）
2. 下游任务是不是在微调的时候确定了，然后直接用微调的评估函数算就行？

| 评测能力     | 任务             | 形式                                                         | 评测指标                                                  |
| ------------ | ---------------- | ------------------------------------------------------------ | --------------------------------------------------------- |
| 基础概念理解 | 化学知识选择题   | 直接选择题<br>输入: [图/文]+问题, 输出: A/B/C/D              | 准确率 (Accuracy)                                         |
| 核心推理过程 | 带思维链的选择题 | CoT + 选择题<br>输入: [图/文]+问题, 输出: 思考过程+最终选项  | 最终答案准确率 (Accuracy)<br>CoT逻辑评分 (GPT-4 Score)    |
| 精确知识提取 | 限制性自由问答   | 简答/填空<br>输入: [图/文]+问题+(格式指令)<br>输出: 特定格式的短文本/数值 | 完全匹配率 (Exact Match) / F1分数<br>数值误差 (RMSE/MAE)  |
| 深度解释能力 | 开放式自由问答   | 自由问答 (Free-form QA)<br>输入: [图/文]+“为什么”类问题<br>输出: 一段解释性文字 | 内容质量评分 (GPT-4 Score)<br>人工评估 (Human Evaluation) |



LLM提出的构建思路：https://g.co/gemini/share/a922edd18da8



#现有工作不足：

1. 单一数据源依赖：
2. 难度梯度设置不足：benchmark内部可能划分了梯度，但单个benchmark难度
3. 化学专精能力评估缺失：
   * 现有化学评估bench[ChemLLMBBench、Mol-Instruction、ChemMLLM]
4. 模态缺失：
   * 化学领域多模态benchmark缺失，MMLU仅专注于语言模型
5. 鲁棒性不足：近期研究表明，LLMs在当前测试基准上对微小扰动并不鲁棒[6,7]，提示词风格或措辞的轻微变化可能导致模型分数显著波动。除此之外，四项选择格式也会加剧评分的不稳定，导致模型评分的聚类性，即多数模型都表现良好导致无法区分[3]。为此，我们通过基于LLMs的选项增强，增强性能区分度并降低随机猜对的概论。
6. 格式局限：选择题形式比如开放式能有效捕捉理解和生成的深度，反映现实场景。
7. 解决方案标注缺失：CoT作为启发MLLMs的重要手段，可以提高模型







# 参考文献：

6开始

```bibtex
@misc{mishra2022crosstaskgeneralizationnaturallanguage,
      title={Cross-Task Generalization via Natural Language Crowdsourcing Instructions}, 
      author={Swaroop Mishra and Daniel Khashabi and Chitta Baral and Hannaneh Hajishirzi},
      year={2022},
      eprint={2104.08773},
      archivePrefix={arXiv},
      primaryClass={cs.CL},
      url={https://arxiv.org/abs/2104.08773}, 
},
@misc{sanh2022multitaskpromptedtrainingenables,
      title={Multitask Prompted Training Enables Zero-Shot Task Generalization}, 
      author={Victor Sanh and Albert Webson and Colin Raffel and Stephen H. Bach and Lintang Sutawika and Zaid Alyafeai and Antoine Chaffin and Arnaud Stiegler and Teven Le Scao and Arun Raja and Manan Dey and M Saiful Bari and Canwen Xu and Urmish Thakker and Shanya Sharma Sharma and Eliza Szczechla and Taewoon Kim and Gunjan Chhablani and Nihal Nayak and Debajyoti Datta and Jonathan Chang and Mike Tian-Jian Jiang and Han Wang and Matteo Manica and Sheng Shen and Zheng Xin Yong and Harshit Pandey and Rachel Bawden and Thomas Wang and Trishala Neeraj and Jos Rozen and Abheesht Sharma and Andrea Santilli and Thibault Fevry and Jason Alan Fries and Ryan Teehan and Tali Bers and Stella Biderman and Leo Gao and Thomas Wolf and Alexander M. Rush},
      year={2022},
      eprint={2110.08207},
      archivePrefix={arXiv},
      primaryClass={cs.LG},
      url={https://arxiv.org/abs/2110.08207}, 
},

```

