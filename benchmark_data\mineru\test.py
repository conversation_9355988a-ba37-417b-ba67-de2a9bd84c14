import copy
import json
import os
from pathlib import Path

from loguru import logger

from mineru.cli.common import convert_pdf_bytes_to_bytes_by_pypdfium2, prepare_env, read_fn
from mineru.data.data_reader_writer import FileBasedDataWriter
from mineru.utils.draw_bbox import draw_layout_bbox, draw_span_bbox
from mineru.utils.enum_class import MakeMode
from mineru.backend.vlm.vlm_analyze import doc_analyze as vlm_doc_analyze
from mineru.backend.pipeline.pipeline_analyze import doc_analyze as pipeline_doc_analyze
from mineru.backend.pipeline.pipeline_middle_json_mkcontent import union_make as pipeline_union_make
from mineru.backend.pipeline.model_json_to_middle_json import result_to_middle_json as pipeline_result_to_middle_json
from mineru.backend.vlm.vlm_middle_json_mkcontent import union_make as vlm_union_make
from mineru.utils.models_download_utils import auto_download_and_get_model_root_path


def do_parse(
    output_dir,  # 用于存储解析结果的输出目录
    pdf_file_names: list[str],  # 要解析的PDF文件名列表
    pdf_bytes_list: list[bytes],  # 要解析的PDF字节列表
    p_lang_list: list[str],  # 每个PDF的语言列表，默认为'ch'（中文）
    backend="pipeline",  # 用于解析PDF的后端，默认为'pipeline'
    parse_method="auto",  # 解析PDF的方法，默认为'auto'
    formula_enable=True,  # 启用公式解析
    table_enable=True,  # 启用表格解析
    server_url=None,  # vlm-sglang-client后端的服务器URL
    f_draw_layout_bbox=True,  # 是否绘制布局边界框
    f_draw_span_bbox=True,  # 是否绘制文本跨度边界框
    f_dump_md=True,  # 是否转储markdown文件
    f_dump_middle_json=True,  # 是否转储中间JSON文件
    f_dump_model_output=True,  # 是否转储模型输出文件
    f_dump_orig_pdf=True,  # 是否转储原始PDF文件
    f_dump_content_list=True,  # 是否转储内容列表文件
    f_make_md_mode=MakeMode.MM_MD,  # 生成markdown内容的模式，默认为MM_MD
    start_page_id=0,  # 解析的起始页面ID，默认为0
    end_page_id=None,  # 解析的结束页面ID，默认为None（解析所有页面直到文档末尾）
):
    """
    执行PDF解析的核心函数
    """
    # 根据选择的后端（pipeline或VLM）执行不同的解析流程
    if backend == "pipeline":
        # 对每个PDF文件进行预处理，根据指定的页面范围提取内容
        for idx, pdf_bytes in enumerate(pdf_bytes_list):
            new_pdf_bytes = convert_pdf_bytes_to_bytes_by_pypdfium2(pdf_bytes, start_page_id, end_page_id)
            pdf_bytes_list[idx] = new_pdf_bytes

        # 使用pipeline后端进行文档分析
        infer_results, all_image_lists, all_pdf_docs, lang_list, ocr_enabled_list = pipeline_doc_analyze(pdf_bytes_list, p_lang_list, parse_method=parse_method, formula_enable=formula_enable,table_enable=table_enable)

        # 处理每个PDF的分析结果
        for idx, model_list in enumerate(infer_results):
            model_json = copy.deepcopy(model_list)
            pdf_file_name = pdf_file_names[idx]
            # 准备输出环境，创建图片和Markdown的输出目录
            local_image_dir, local_md_dir = prepare_env(output_dir, pdf_file_name, parse_method)
            image_writer, md_writer = FileBasedDataWriter(local_image_dir), FileBasedDataWriter(local_md_dir)

            images_list = all_image_lists[idx]
            pdf_doc = all_pdf_docs[idx]
            _lang = lang_list[idx]
            _ocr_enable = ocr_enabled_list[idx]
            # 将模型输出结果转换为中间格式JSON
            middle_json = pipeline_result_to_middle_json(model_list, images_list, pdf_doc, image_writer, _lang, _ocr_enable, formula_enable)

            pdf_info = middle_json["pdf_info"]

            pdf_bytes = pdf_bytes_list[idx]
            # 根据选项决定是否绘制并保存带有布局边界框的PDF
            if f_draw_layout_bbox:
                draw_layout_bbox(pdf_info, pdf_bytes, local_md_dir, f"{pdf_file_name}_layout.pdf")

            # 根据选项决定是否绘制并保存带有文本跨度边界框的PDF
            if f_draw_span_bbox:
                draw_span_bbox(pdf_info, pdf_bytes, local_md_dir, f"{pdf_file_name}_span.pdf")

            # 根据选项决定是否保存原始PDF文件
            if f_dump_orig_pdf:
                md_writer.write(
                    f"{pdf_file_name}_origin.pdf",
                    pdf_bytes,
                )

            # 根据选项决定是否生成并保存Markdown文件
            if f_dump_md:
                image_dir = str(os.path.basename(local_image_dir))
                md_content_str = pipeline_union_make(pdf_info, f_make_md_mode, image_dir)
                md_writer.write_string(
                    f"{pdf_file_name}.md",
                    md_content_str,
                )

            # 根据选项决定是否生成并保存内容列表JSON文件
            if f_dump_content_list:
                image_dir = str(os.path.basename(local_image_dir))
                content_list = pipeline_union_make(pdf_info, MakeMode.CONTENT_LIST, image_dir)
                md_writer.write_string(
                    f"{pdf_file_name}_content_list.json",
                    json.dumps(content_list, ensure_ascii=False, indent=4),
                )

            # 根据选项决定是否保存中间格式JSON文件
            if f_dump_middle_json:
                md_writer.write_string(
                    f"{pdf_file_name}_middle.json",
                    json.dumps(middle_json, ensure_ascii=False, indent=4),
                )

            # 根据选项决定是否保存模型原始输出JSON文件
            if f_dump_model_output:
                md_writer.write_string(
                    f"{pdf_file_name}_model.json",
                    json.dumps(model_json, ensure_ascii=False, indent=4),
                )

            logger.info(f"本地输出目录位于 {local_md_dir}")
    else:
        # VLM后端处理流程
        if backend.startswith("vlm-"):
            backend = backend[4:]

        f_draw_span_bbox = False
        parse_method = "vlm"
        for idx, pdf_bytes in enumerate(pdf_bytes_list):
            pdf_file_name = pdf_file_names[idx]
            pdf_bytes = convert_pdf_bytes_to_bytes_by_pypdfium2(pdf_bytes, start_page_id, end_page_id)
            local_image_dir, local_md_dir = prepare_env(output_dir, pdf_file_name, parse_method)
            image_writer, md_writer = FileBasedDataWriter(local_image_dir), FileBasedDataWriter(local_md_dir)
            # 使用VLM后端进行文档分析
            middle_json, infer_result = vlm_doc_analyze(pdf_bytes, image_writer=image_writer, backend=backend, server_url=server_url)

            pdf_info = middle_json["pdf_info"]

            if f_draw_layout_bbox:
                draw_layout_bbox(pdf_info, pdf_bytes, local_md_dir, f"{pdf_file_name}_layout.pdf")

            if f_draw_span_bbox:
                draw_span_bbox(pdf_info, pdf_bytes, local_md_dir, f"{pdf_file_name}_span.pdf")

            if f_dump_orig_pdf:
                md_writer.write(
                    f"{pdf_file_name}_origin.pdf",
                    pdf_bytes,
                )

            if f_dump_md:
                image_dir = str(os.path.basename(local_image_dir))
                md_content_str = vlm_union_make(pdf_info, f_make_md_mode, image_dir)
                md_writer.write_string(
                    f"{pdf_file_name}.md",
                    md_content_str,
                )

            if f_dump_content_list:
                image_dir = str(os.path.basename(local_image_dir))
                content_list = vlm_union_make(pdf_info, MakeMode.CONTENT_LIST, image_dir)
                md_writer.write_string(
                    f"{pdf_file_name}_content_list.json",
                    json.dumps(content_list, ensure_ascii=False, indent=4),
                )

            if f_dump_middle_json:
                md_writer.write_string(
                    f"{pdf_file_name}_middle.json",
                    json.dumps(middle_json, ensure_ascii=False, indent=4),
                )

            if f_dump_model_output:
                model_output = ("\n" + "-" * 50 + "\n").join(infer_result)
                md_writer.write_string(
                    f"{pdf_file_name}_model_output.txt",
                    model_output,
                )

            logger.info(f"本地输出目录位于 {local_md_dir}")


def parse_doc(
        path_list: list[Path],
        output_dir,
        lang="ch",
        backend="pipeline",
        method="auto",
        server_url=None,
        start_page_id=0,
        end_page_id=None
):
    """
        参数说明:
        path_list: 要解析的文档路径列表，可以是PDF或图片文件。
        output_dir: 用于存储解析结果的输出目录。
        lang: 语言选项, 默认为 'ch', 可选值包括['ch', 'ch_server', 'ch_lite', 'en', 'korean', 'japan', 'chinese_cht', 'ta', 'te', 'ka']。
            输入PDF中的语言（如果已知）以提高OCR准确性。可选。
            仅在后端设置为“pipeline”时适用。
        backend: 用于解析PDF的后端:
            pipeline: 更通用。
            vlm-transformers: 更通用。
            vlm-sglang-engine: 更快（引擎）。
            vlm-sglang-client: 更快（客户端）。
            未指定方法时，默认使用pipeline。
        method: 解析PDF的方法:
            auto: 根据文件类型自动确定方法。
            txt: 使用文本提取方法。
            ocr: 对基于图像的PDF使用OCR方法。
            未指定方法时，默认使用'auto'。
            仅在后端设置为“pipeline”时适用。
        server_url: 当后端为`sglang-client`时，需要指定server_url，例如：`http://127.0.0.1:30000`
        start_page_id: 解析的起始页面ID，默认为0
        end_page_id: 解析的结束页面ID，默认为None（解析所有页面直到文档末尾）
    """
    try:
        file_name_list = []
        pdf_bytes_list = []
        lang_list = []
        # 遍历输入的文件路径列表，读取文件内容
        for path in path_list:
            file_name = str(Path(path).stem)
            pdf_bytes = read_fn(path)
            file_name_list.append(file_name)
            pdf_bytes_list.append(pdf_bytes)
            lang_list.append(lang)
        # 调用核心解析函数
        do_parse(
            output_dir=output_dir,
            pdf_file_names=file_name_list,
            pdf_bytes_list=pdf_bytes_list,
            p_lang_list=lang_list,
            backend=backend,
            parse_method=method,
            server_url=server_url,
            start_page_id=start_page_id,
            end_page_id=end_page_id
        )
    except Exception as e:
        logger.exception(e)


if __name__ == '__main__':
    # 强制重新下载模型
    os.environ['MINERU_MODEL_SOURCE'] = "modelscope"
    os.environ['MINERU_FORCE_DOWNLOAD'] = "1"  # 强制重新下载
    
    # 设置PDF文件目录和输出目录
    __dir__ = os.path.dirname(os.path.abspath(__file__))
    pdf_files_dir = os.path.join(__dir__, "pdfs")
    output_dir = os.path.join(__dir__, "output")
    pdf_suffixes = [".pdf"]
    image_suffixes = [".png", ".jpeg", ".jpg"]

    # 获取所有待处理的文档路径
    doc_path_list = []
    for doc_path in Path(pdf_files_dir).glob('*'):
        if doc_path.suffix in pdf_suffixes + image_suffixes:
            doc_path_list.append(doc_path)

    """如果您由于网络问题无法下载模型，可以设置环境变量MINERU_MODEL_SOURCE为modelscope使用免代理仓库下载模型"""
    os.environ['MINERU_MODEL_SOURCE'] = "modelscope"

    """如果您的环境不支持VLM，请使用pipeline模式"""
    parse_doc(doc_path_list, output_dir, backend="pipeline")

    """要启用VLM模式，请将后端更改为'vlm-xxx'"""
    # parse_doc(doc_path_list, output_dir, backend="vlm-transformers")  # 更通用
    # parse_doc(doc_path_list, output_dir, backend="vlm-sglang-engine")  # 更快（引擎）
    # parse_doc(doc_path_list, output_dir, backend="vlm-sglang-client", server_url="http://127.0.0.1:30000")  # 更快（客户端）

